import React, {
  useEffect,
  useState, // useEffect,
} from "react";
import { useSelector } from "react-redux";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import {GoogleReCaptchaProvider} from "react-google-recaptcha-v3";

// import { AppLayout } from "..";
import { Header, Footer } from "../../components";
import { selectUserData } from "../../redux/AuthSlice/index.slice";
import { moduleRoutesList } from "../../routes/index";
import AppLayout from "../Auth/index.layout";

function PrivateLayout() {
  const userData = useSelector(selectUserData);
  // let routeList = moduleRoutesList();

  const navigate = useNavigate();
  const [redirectpath, setRedirectPath] = useState("");

  useEffect(() => {
    if (redirectpath) {
      navigate(redirectpath);
    }
  }, [redirectpath]);

  const location = useLocation();
  const { pathname } = location;
  let path = pathname.replace("/", "");
  path = path === "" ? "home" : path;
  path = path.includes("invitee") ? "invitee" : path;
  path = path.includes("meeting") ? "invitee" : path;
  useEffect(() => {
    const mainCnt = document.querySelector(".mainContent");
    if (["home"].includes(path)) {
      mainCnt?.classList.add("bg-white");
    } else {
      mainCnt?.classList.remove("bg-white");
    }
  });

  const getmoduleRoutesList = moduleRoutesList();
  let user = "user";

  useEffect(() => {
    if (JSON.stringify(userData) !== "{}" && path !== "screenshare-overlay" && path !== "controlbar" )
      setTimeout(() => {
        let navbar = document.querySelector(".navbar");
        let footer = document.querySelector(".footerSec");
        let navbarHeight = navbar?.clientHeight;
        let footerHeight = footer?.clientHeight;
        document.querySelector(".mainContent").style.minHeight = `${
          window.innerHeight - (navbarHeight + footerHeight)
        }px`;
      }, 300);
  }, []);

  return (
    <AppLayout setRedirectPath={setRedirectPath}>
      <div className="mainBody">
        {(() => {
          // Special paths that don't need header/footer
          const specialPaths = [
            "invitee", 
            "screenshare-overlay", 
            "controlbar", 
            "login-app", 
            "continue-to-app", 
            "complete-login-app", 
            "after-sign-in"
          ];
          
          // Check if current path is a special path
          const isSpecialPath = specialPaths.includes(path);
          
          // Check if path starts with recordings or whiteboard
          const isRecordingOrWhiteboard = path.startsWith("recordings/") || path.startsWith("whiteboard/") || path.startsWith("recording/");
          
          // Return appropriate layout based on path
          if (isSpecialPath || isRecordingOrWhiteboard) {
            return (
              <div className={path === "screenshare-overlay" ? "transparentContent" : "mainContent"}>
                <Outlet />
              </div>
            );
          } else {
            return (
              <>
                <Header routes={getmoduleRoutesList?.[user]} />
                <div className="mainContent">
                  <Outlet />
                </div>
                <GoogleReCaptchaProvider reCaptchaKey={process.env.REACT_APP_GOOGLE_CAPTCHA_KEY}>
                  <Footer routes={getmoduleRoutesList?.[user]} />
                </GoogleReCaptchaProvider>
              </>
            );
          }
        })()}
      </div>
    </AppLayout>
  );
}

export default PrivateLayout;
