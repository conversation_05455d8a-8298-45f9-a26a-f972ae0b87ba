import React, { useEffect, useState } from "react";
// import { useSelector } from "react-redux";
import { Outlet, useNavigate } from "react-router-dom";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";
import { <PERSON><PERSON>, AdminHeader, AdminFooter } from "../../components/index";
import { moduleRoutesList } from "../../routes";
import AppLayout from "../Auth/index.layout";
// import { selectUserData } from "../../redux/AuthSlice/index.slice";
// import { routesList } from "../../routes/index";

function AdminPrivateLayout() {
  // const userData = useSelector(selectUserData);
  let routeList = moduleRoutesList();
  const navigate = useNavigate();
  const [redirectpath, setRedirectPath] = useState("");

  useEffect(() => {
    if (redirectpath) {
      navigate(redirectpath);
    }
  }, [redirectpath]);

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const menuToggle = () => {
    if(window.innerWidth <= 1199 ){
      setSidebarOpen(!sidebarOpen);
      const list = document.querySelector("body");
      list?.classList.toggle("nav-shown");
    }
  };

  return (
    <AppLayout setRedirectPath={setRedirectPath}>
      <div className="nk-body bg-lighter npc-general has-sidebar">
        <div className="nk-app-root">
          <div className="nk-main ">
            <Sidebar
              sidebarOpen={sidebarOpen}
              menuToggle={menuToggle}
              routes={routeList.admin}
            />
            {sidebarOpen ? (
              <div
                onClick={() => menuToggle()}
                className="nk-sidebar-overlay"
              />
            ) : (
              ""
            )}
            <div className="nk-wrap">
              <AdminHeader menuToggle={menuToggle} />
              <div className="nk-content">
                <div className="container-fluid">
                  <div className="nk-content-inner">
                    <div className="nk-content-body">
                    <GoogleReCaptchaProvider reCaptchaKey={process.env.REACT_APP_GOOGLE_CAPTCHA_KEY}>
                      <Outlet />
                    </GoogleReCaptchaProvider>
                    </div>
                  </div>
                </div>
              </div>
              <AdminFooter />
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}

export default AdminPrivateLayout;
