export const validation = {
  eventType: "Event type is required",
  eventName: "Event name is required",
  "Conference-host": "Host Name is required",
  "Class-host": "Teacher Name is required",
  "Webinar-host": "Speaker is required",
  "TeleMed-host": "Doctor’s Name is required",
  "Panel Discussion-host": "Host Name is required",
  "Conference-name": "Event name is required",
  "Class-name": "Class Name is required",
  "Webinar-name": "Title is required",
  "TeleMed-name": "Patient’s  Name is required",
  "Panel Discussion-name": "Event Name is required",
  "TeleMed-topic": "Department  is required",
  "Panel Discussion-topic": "Moderator is required",
  subject: "Subject is required",
  location: "Location is required",
  timeZone: "Time zone is required",
  startDate: " Start date is required",
  appointmentDate: " Appointment date is required",
  startTime: "Start time is required",
  appointmentTime: "Time is required",
  duration: "Duration is required",
  recurrence: "Recurrence is required",
  repeat: "Repeat Every Day(s) is required",
  eventMode: "Event mode is required",
  endDate: "End date is required",
  occurrences: "Occurrences is required",
  meeting: "Meeting Name is required",
  eventNameValidation: "Special characters are not allowed",
  meetingNameValidation: "Special characters are not allowed",
  // email: "Email is required",
  email: "Email is mandatory as each participant will receive a unique password to join the meeting.",
};
