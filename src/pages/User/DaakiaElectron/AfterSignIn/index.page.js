import React from "react";
import "../HomeElectron/HomeElectron.scss";
import { MdWindow } from "react-icons/md";
import { FaApple } from "react-icons/fa";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { selectProfileData } from "../../../../redux/UserSlice/index.slice";
import { ReactComponent as Logo } from "../HomeElectron/Assets/DaakiaLogo.svg";
import userRoutesMap from "../../../../routeControl/userRoutes";
import {
  redirectToDesktopAppForLogin,
  triggerDownloadByOS,
} from "../../../../utils";
import { OS_PLATFORM } from "../../../../utils/constants";

export default function AfterSignIn() {
  const userData = useSelector(selectProfileData);
  const navigate = useNavigate();

  return (
    <div className="home-electron home-electron-after-sign-in">
      <Logo className="home-electron-logo" />
      <h2 className="home-elctron-head">
        You&apos;ve signed in successfully 🎉
      </h2>
      <p className="home-electron-sub-head">Continue with</p>
      <div className="home-electron-after-sign-in-btns">
        <a
          // href="/user/daakia-electron/home"
          className="home-electron-btn"
          onClick={() => {
            redirectToDesktopAppForLogin(userData?.username);
          }}
        >
          Desktop App
        </a>
        <p className="home-electron-sub-head">or</p>
        <a
          // href="/user/daakia-electron/home"
          className="home-electron-btn"
          onClick={() => {
            navigate(userRoutesMap.USER_DASHBOARD.path);
          }}
        >
          Web Browser
        </a>
      </div>
      <div className="home-electron-after-sign-in-download">
        <p className="home-electron-sub-head">
          Don&apos;t have the desktop app? <span>Download one</span>
        </p>
        <div className="home-electron-after-sign-in-download-btns">
          <a
            // href="https://cdn.vc.daakia.co.in/public-releases/DaakiaWinApp-Beta.exe.zip"
            onClick={(e) => {
              e.preventDefault();
              triggerDownloadByOS(OS_PLATFORM.WINDOWS);
            }}
            target="_blank"
            rel="noreferrer"
            className="home-electron-download"
          >
            <MdWindow />
            Windows
          </a>
          <a
            // href="https://cdn.vc.daakia.co.in/public-releases/DaakiaMacApp-Beta.dmg.zip"
            onClick={(e) => {
              e.preventDefault();
              triggerDownloadByOS(OS_PLATFORM.MAC);
            }}
            target="_blank"
            rel="noreferrer"
            className="home-electron-download"
          >
            <FaApple />
            Mac OS
          </a>
        </div>
      </div>
    </div>
  );
}
