/* eslint-disable */
import React, { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  UserHomeServices,
  UserProfileServices,
  UserServices,
} from "../../../../services/User";
import {
  getActiveAccount,
  getIscorporateActive,
  logger,
  modalNotification,
  removeLocalStorageToken,
  setLocalStorageToken,
  getLocalStorage,
  setLocalStorage,
} from "../../../../utils";
import { login } from "../../../../redux/AuthSlice/index.slice";
import {
  getLanguageList,
  getProfile,
  getUserAccount,
  updateUserSubscription,
} from "../../../../redux/UserSlice/index.slice";
import userRoutesMap from "../../../../routeControl/userRoutes";
import "../HomeElectron/HomeElectron.scss";
import { Loader } from "../../../../components";
import isElectron from "is-electron";
import {ReactComponent as Logo} from "../HomeElectron/Assets/DaakiaLogo.svg";

export default function CompleteElectronLogin() {

  const [isLoadingDesktopApp, setIsLoadingDesktopApp] = useState(false);

  let dispatch = useDispatch();
  const navigate = useNavigate();

  const handleLoginUserForDesktop = async (userId) => {
    try {
      if (isLoadingDesktopApp ) return; // Skip if already loading
      setIsLoadingDesktopApp(true); // Mark as loading
      // Get user token for desktop
      const response = await UserServices.getUserTokenForDesktopService({
        userJid: userId,
      });
      const { success, data, message } = response;
      const resData = data;
      resData.userRole = "user";
      if (success === 1) {
        // Set token in local storage
        setLocalStorageToken(data?.jwt);

        // add delay to properly set token in local storage
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Get user account
        const responseaccount = await UserProfileServices.getAccountService();
        const activeAccount = getActiveAccount(responseaccount?.data);

        let queryParams = {
          is_corporate: getIscorporateActive(responseaccount?.data),
          corporate_id: activeAccount?.id,
        };

        // Get user subscription
        const res = await UserHomeServices.userActiveSubscriptionListingService(
          {
            queryParams,
          }
        );

        if (res?.success === 1) {
          dispatch(updateUserSubscription(res?.data));
          dispatch(login(resData));
          dispatch(getProfile());
          dispatch(getUserAccount());
          dispatch(getLanguageList());
          // Sigin success
          setLocalStorage("isLoggedOut", false);
          modalNotification({
            type: "success",
            message: "Sign in successful",
          });

          // Redirect to user dashboard
          navigate(userRoutesMap.USER_DASHBOARD.path);
        } else {
          removeLocalStorageToken();
          modalNotification({
            type: "error",
            message: res?.message,
          });
        }
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    } finally {
      setIsLoadingDesktopApp(false); // Mark as loading
    }
  };

  // Redirect to login page of app
  const redirectToLogin = () => {
    navigate(userRoutesMap.HOME_ELECTRON.path);
  };

  // add listener for window focus
  const focusHandled = useRef(false);
  // Get user ID from Electron Store
  useEffect(() => {
    // Function to get user ID from Electron Store
    let isLoggedOut = getLocalStorage("isLoggedOut");
    const getUserId = async () => {
      if (isLoggedOut) return;
      const userId = await window.electronAPI.ipcRenderer.invoke(
        "get-store-value",
        "user_id"
      );
      if (userId && (userId !== undefined || userId !== null)) {
        await handleLoginUserForDesktop(userId);
      }
    };

    // Handle the window focus event
    const handleWindowFocus = () => {
      isLoggedOut = false;
      getUserId();
    };

    // Add event listener for 'window-focused' event
    if (isElectron() && !focusHandled.current) {
      focusHandled.current = true; // Mark as handled
      window.electronAPI.ipcRenderer.on("window-focused", handleWindowFocus);
    }

    // Cleanup the event listener on unmount
    return () => {
      if (isElectron()) {
        window.electronAPI.ipcRenderer.removeListener(
          "window-focused",
          handleWindowFocus
        );
        focusHandled.current = false;
      }
    };
  }, []);

  return isLoadingDesktopApp ? (
    <div
      style={{
        display: "flex",
        flexDirection: "column", // Stack items vertically
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "100vh",
      }}
    >
      <div style={{ marginBottom: "20px", fontSize: "1.2rem", color: "#333" }}>
        Please wait!! While we log you in...
      </div>
      <Loader />
    </div>
  ) : (
    <div className="home-electron">
      <Logo className="home-electron-logo" />
      <h2 className="home-elctron-head">Authenticate</h2>
      <h2 className="home-elctron-head">Login Through Browser</h2>
      <span className="home-elctron-head-span">
        Can&apos;t find the login screen?{" "}
        <a
          onClick={() => redirectToLogin()}
          className="home-electon-home-try-again"
        >
          Click here to go back and try again.
        </a>
      </span>
    </div>
  );
}
