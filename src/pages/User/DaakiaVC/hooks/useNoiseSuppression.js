import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';


export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessor = useRef(null);
  const originalTrack = useRef(null);
  const savedDeviceId = useRef(null);

  const getMicState = () => {
    const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
    const currentTrackDeviceId = micPublication?.track?.mediaStreamTrack?.getSettings()?.deviceId;

    const isDeviceChanged = savedDeviceId.current && currentTrackDeviceId &&
                           savedDeviceId.current !== currentTrackDeviceId &&
                           savedDeviceId.current !== 'undefined' &&
                           currentTrackDeviceId !== 'undefined';

    return {
      micPublication,
      isEnabled: micPublication?.isEnabled || false,
      hasTrack: !!micPublication?.track,
      isMuted: micPublication?.isMuted || false,
      isPublished: micPublication?.track && !micPublication?.isMuted,
      hasNoiseProcessor: !!noiseProcessor.current,
      currentTrackDeviceId,
      savedDeviceId: savedDeviceId.current,
      selectedDeviceId: deviceIdAudio,
      isDeviceChanged
    };
  };

  const applyNoiseSuppression = async () => {
    try {
      const { micPublication, currentTrackDeviceId } = getMicState();
      const localAudioTrack = micPublication.track;

      originalTrack.current = localAudioTrack.mediaStreamTrack;
      savedDeviceId.current = currentTrackDeviceId;

      noiseProcessor.current = new NoiseSuppressionProcessor();
      const processedTrack = await noiseProcessor.current.startProcessing(
        localAudioTrack.mediaStreamTrack
      );

      if (processedTrack) {
        await localAudioTrack.replaceTrack(processedTrack, true);
      }
    } catch (error) {
      console.error('❌ Error applying noise suppression:', error);
    }
  };

  const stopNoiseSuppression = async () => {
    try {
      if (noiseProcessor.current) {
        const { micPublication } = getMicState();

        if (micPublication?.track && originalTrack.current) {
          await micPublication.track.replaceTrack(originalTrack.current, true);
        }

        noiseProcessor.current.stopProcessing();
        noiseProcessor.current = null;
        originalTrack.current = null;
        savedDeviceId.current = null;
      }
    } catch (error) {
      console.error('❌ Error stopping noise suppression:', error);
    }
  };

  const handleNoiseSuppressionLogic = async () => {
    const {
      isEnabled,
      hasTrack,
      isMuted,
      isPublished,
      hasNoiseProcessor,
      currentTrackDeviceId,
      selectedDeviceId,
      isDeviceChanged
    } = getMicState();

    // NS Toggle is OFF
    if (!isNoiseSuppressionEnabled) {
      if (hasNoiseProcessor) {
        await stopNoiseSuppression();
      }
      return;
    }

    // Device changed - handle with toggle disable/enable
    if (isDeviceChanged && hasNoiseProcessor && currentTrackDeviceId && selectedDeviceId) {
      if (currentTrackDeviceId === selectedDeviceId) {
        await stopNoiseSuppression();

        setIsNoiseSuppressionEnabled(false);

        setTimeout(() => {
          setIsNoiseSuppressionEnabled(true);
        }, 700);

        return;
      }
    }

    // NS Toggle is ON but no mic track
    if (!hasTrack) {
      return;
    }

    // NS Toggle is ON, mic exists but muted
    if (isMuted) {
      if (hasNoiseProcessor) {
        return;
      } else {
        return;
      }
    }

    // NS Toggle is ON, mic unmuted and published
    if (isPublished) {
      if (hasNoiseProcessor) {
        return;
      } else {
        setTimeout(async () => {
          try {
            await applyNoiseSuppression();
          } catch (error) {
            console.error('❌ Delayed noise suppression error:', error);
          }
        }, 1000);

        return;
      }
    }

    // Mic disabled - no action needed
    if (!isEnabled) {
      // No action needed when mic is disabled
    }
  };

  useEffect(() => {
    if (!room?.localParticipant) return;
    handleNoiseSuppressionLogic();
  }, [
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isEnabled,
    deviceIdAudio,
    isNoiseSuppressionEnabled,
    isNoiseSuppressionEnabled && !noiseProcessor.current ? 
      room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isMuted : null
  ]);

  useEffect(() => {
    return () => {
      stopNoiseSuppression();
    };
  }, []);

  return {
    isNoiseSuppressionActive: !!noiseProcessor.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression
  };
};
