import { <PERSON><PERSON>, <PERSON><PERSON>, Popover } from "antd";
import React, { useState, useCallback } from "react";
import { useTracks } from "@livekit/components-react";
import { Track } from "livekit-client";

import { getLocalStorageToken,parseMetadata } from "../../utils/helper";
import { DataReceivedEvent, DrawerState } from "../../utils/constants";
import { ParticipantService } from "../../services/ParticipantServices";
import { ReactComponent as ThreeDots } from "./icons/ThreeDots.svg";
import "./styles/ParticipantPopover.scss";
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";

export function ParticipantsPopOver({
  participant,
  localParticipant,
  meetingUid,
  isHost,
  layoutContext,
  isCoHost,
  coHostToken,
  meetingFeatures,
  forcemute,
  forcevideooff,
  setselectedprivatechatparticipant,
  setprivatechatparticipants,
  privatechatparticipants,
  setshowprivatechat,
  room,
  isBreakoutRoom,
  breakoutRooms,
  allowLiveCollabWhiteBoard,
  numberOfCohostCurrent,
  setNumberOfCohostCurrent,
  setToastNotification,
  setToastStatus,
  setShowToast,
  isPinned,
}) {
  const { setOpenDrawer } = useVideoConferencesContext();
  const { isSaaS, saasHostToken } = useSaasHelpers();
  const cameraTracksReferences = useTracks([Track.Source.Camera]);
  const [showPopover, setShowPopover] = useState(false);
  const [isRemoveParticipantModalVisible, setIsRemoveParticipantModalVisible] = useState(false);

  const removeParticipant = useCallback(async (id, p) => {
    try {
      if (isBreakoutRoom) {
        await ParticipantService.removeParticipant(
          room?.roomInfo?.name,
          p.identity,
          localParticipant?.participantInfo,
          saasHostToken || coHostToken,
        );
      } else {
        await ParticipantService.removeParticipant(
          id,
          p.identity,
          localParticipant?.participantInfo,
          saasHostToken || coHostToken,
        );
      }
      setToastNotification("Participant removed successfully.");
      setToastStatus("success");
      setShowToast(true);
    } catch (error) {
      setToastNotification("Error removing participant.");
      setToastStatus("error");
      setShowToast(true);
      // console.log("Error removing participant:", err);
    }
  }, []);

  const sendPrivateMessage = () => {
    if (privatechatparticipants.length > 0) {
      const participantIndex = privatechatparticipants.findIndex(
        (part) => part.participant.identity === participant.identity
      );
      if (participantIndex !== -1) {
        setselectedprivatechatparticipant(
          privatechatparticipants[participantIndex]
        );
      } else {
        const maxKey = privatechatparticipants.reduce(
          (max, p) => Math.max(max, p.key),
          0
        );
        const newParticipant = {
          key: maxKey + 1,
          participant,
          isConnected: true,
          receivedUnreadMessagesCount: 0,
        };
        // Add the new participant to the list (this may involve setting state)
        setprivatechatparticipants([
          ...privatechatparticipants,
          newParticipant,
        ]);
        setselectedprivatechatparticipant(newParticipant);
      }
    } else {
      const newParticipant = {
        key: 1,
        participant,
        isConnected: true,
        receivedUnreadMessagesCount: 0,
      };
      setprivatechatparticipants([newParticipant]);
      setselectedprivatechatparticipant(newParticipant);
    }
    setshowprivatechat(true);
    setOpenDrawer((prev) => prev === DrawerState.CHAT ? DrawerState.NONE : DrawerState.CHAT);
    // dispatch({ msg: "show_chat" });
  };

  const askToUnmuteMic = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.ASK_TO_UNMUTE_MIC,
        })
      );
      await localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const muteMic = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.MUTE_MIC,
        })
      );
      await localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const askToUnmuteCamera = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.ASK_TO_UNMUTE_CAMERA,
        })
      );
      await localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const muteCamera = useCallback(
    async (p) => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.MUTE_CAMERA,
        })
      );
      await localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [p.identity],
      });
    },
    [localParticipant]
  );

  const pinToScreen = useCallback(() => {
    try {
      if (!isPinned.current) {
        const participantTrackRef = cameraTracksReferences.find(
          (track) => track.participant.identity === participant.identity
        );
        if (!participantTrackRef) {
          setToastNotification("Cannot pin participant without camera.");
          setToastStatus("warning");
          setShowToast(true);
          return;
        }
        isPinned.current=true;
        layoutContext.pin.dispatch?.({
          msg: "set_pin",
          trackReference: participantTrackRef,
        });
        setToastNotification(`${participant.name} pinned to screen.`);
        setToastStatus("success");
        setShowToast(true);
      } else {
        layoutContext.pin.dispatch?.({
          msg: "clear_pin",
        });
        isPinned.current=false;
        setToastNotification("Participant unpinned from screen.");
        setToastStatus("success");
        setShowToast(true);
      }
    } catch (error) {
      setToastNotification("Failed to pin participant.");
      setToastStatus("error");
      setShowToast(true);
    }
  }, [
    participant,
    layoutContext,
    isPinned.current,
    localParticipant,
    cameraTracksReferences,
  ]);

  const makeCohost = useCallback(async () => {
    try {
      const participantAlreadyCohost =
        parseMetadata(participant.metadata)?.role_name === "cohost";
      const response = await ParticipantService.makeCoHost(
        meetingUid,
        participant,
        localParticipant?.participantInfo,
        (
          (isSaaS && isHost && saasHostToken) ||
          (isSaaS && isCoHost && coHostToken) ||
          (!isSaaS && isHost && getLocalStorageToken()) ||
          (!isSaaS && isCoHost && coHostToken) ||
          null
        )
      );
      if (response.success === 0) {
        setToastNotification("Error making co-host");
        setToastStatus("error");
        setShowToast(true);
        return;
      }
      const encoder = new TextEncoder();
      let data;
      if (participantAlreadyCohost) {
        data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.REMOVE_CO_HOST,
            user: {
              name: localParticipant?.participantInfo?.name,
            },
          })
        );
        setNumberOfCohostCurrent((prev) => prev - 1);
        setToastNotification(`${participant.name} removed as co-host`);
      } else {
        data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.MAKE_CO_HOST,
            token: isSaaS
              ? (isHost
                  ? saasHostToken
                  : isCoHost
                  ? coHostToken
                  : null)
              : (isHost
                  ? getLocalStorageToken()
                  : isCoHost
                  ? coHostToken
                  : null),
            forceMute: forcemute,
            forceVideoOff: forcevideooff,
            breakoutRooms,
            allowLiveCollabWhiteBoard,
            user: {
              name: localParticipant?.participantInfo?.name,
            },
          })
        );
        setNumberOfCohostCurrent((prev) => prev + 1);
        setToastNotification(`${participant.name} made co-host`);
      }
      await localParticipant.publishData(data, {
        reliable: true,
        destinationIdentities: [participant.identity],
      });
      setToastStatus("success");
      setShowToast(true);
    } catch (error) {
      setToastNotification("Error making co-host");
      setToastStatus("error");
      setShowToast(true);
    }
  }, [localParticipant, meetingUid, participant]);

  const content = (
    <div className="pp-menu">
      {!participant.isMicrophoneEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              askToUnmuteMic(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Ask To Unmute Mic</div>
          </div>
        )}

      {participant.isMicrophoneEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              muteMic(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Mute Mic</div>
          </div>
        )}
      {!participant.isCameraEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              askToUnmuteCamera(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Ask To TurnON Camera</div>
          </div>
        )}

      {participant.isCameraEnabled &&
        meetingFeatures?.mute_participant === 1 &&
        (isHost || isCoHost) && (
          <div
            className="pp-menu-item"
            onClick={() => {
              muteCamera(participant);
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">Turn Off Camera</div>
          </div>
        )}
      {(isSaaS
        ? (isHost || isCoHost) && (
            meetingFeatures?.configurations?.allow_multiple_cohost === 1 ||
            numberOfCohostCurrent < 1
          ) && parseMetadata(participant.metadata)?.role_name !== "moderator"
        : (isHost || isCoHost) && parseMetadata(participant.metadata)?.role_name !== "moderator" ) 
          && (
          <div
            className="pp-menu-item"
            onClick={() => {
              makeCohost();
              setShowPopover(false);
            }}
          >
            <div className="pp-menu-inner-text">
              {parseMetadata(participant.metadata)?.role_name === "cohost"
                ? "Remove Co-Host"
                : "Make Co-Host"}
            </div>
          </div>
      )}
      <div className="pp-menu-item" onClick={() => pinToScreen()}>
        <div className="pp-menu-inner-text">
          {isPinned.current ? "Remove Pin" : "Pin To Screen"}
        </div>
      </div>
      {(isHost ||
        (isCoHost &&
          parseMetadata(participant.metadata)?.role_name !== "moderator")) && (
        <div
          className="pp-menu-item"
          onClick={() => {
            setIsRemoveParticipantModalVisible(true);
            setShowPopover(false);
          }}
        >
          <div className="pp-menu-inner-text">Remove From Call</div>
        </div>
      )}
      {(isSaaS ? meetingFeatures?.configurations?.enable_private_chat === 1 : true) && (
        <div className="pp-menu-item" onClick={() => sendPrivateMessage()}>
          <div className="pp-menu-inner-text">Send Private Message</div>
        </div>
      )}
    </div>
  );

  return (
    <>
      <Popover
        content={content}
        title={null}
        trigger="click"
        open={showPopover}
        placement="leftTop"
        overlayInnerStyle={{
          backgroundColor: "#1c1c1e",
        }}
        onOpenChange={() => setShowPopover(!showPopover)}
        overlayClassName="pp-menu-outer"
      >
        <div style={{ cursor: "pointer" }} className="ppo-icon-participant">
          <ThreeDots />
        </div>
      </Popover>
      <Modal
        open={isRemoveParticipantModalVisible}
        onOk={() => {
          removeParticipant(meetingUid, participant);
          setIsRemoveParticipantModalVisible(false);
        }}
        onCancel={() => setIsRemoveParticipantModalVisible(false)}
        footer={null}
        className="remove-participant-modal"
      >
        <p className="remove-participant-modal-text">Are you sure you want to remove <span>{participant.name}</span> ?</p>
        <div className="remove-participant-modal-buttons">
          <Button
            onClick={() => {
              removeParticipant(meetingUid, participant);
              setIsRemoveParticipantModalVisible(false);
            }}
            type="primary"
          >
            Yes
          </Button>
          <Button onClick={() => setIsRemoveParticipantModalVisible(false)}>
            No
          </Button>
        </div>
      </Modal>
    </>
  );
}
