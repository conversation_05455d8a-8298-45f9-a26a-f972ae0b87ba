import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { useDispatch } from "react-redux";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import adminRoutesMap from "../../../routeControl/adminRoutes";
import { encryptCryptoJsValue, logger, modalNotification } from "../../../utils";
import { AdminAuthServices } from "../../../services/Admin";
import { forgetPassword } from "../../../redux/AuthSlice/index.slice";
import { AdminForgotPasswordForm } from "../../../components";

function ForgotPassword() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  let dispatch = useDispatch();
  const { executeRecaptcha } = useGoogleReCaptcha();

  const onSubmit = async (values) => {
    setLoading(true);
    try {
      let bodyData = { ...values };
      const token = await executeRecaptcha("admin_forgot_password");
      const encryptedData = {
        email: await encryptCryptoJsValue(bodyData.email, process.env.REACT_APP_ADMIN_LOGIN_ENCRYPT_SALT_KEY),
        recaptcha_token: token
      };
      const response = await AdminAuthServices.forgetPasswordService(encryptedData);
      const { success, message } = response;
      if (success === 1) {
        modalNotification({
          type: "success",
          message,
        });
        dispatch(forgetPassword(bodyData));
        navigate(adminRoutesMap.OTP_VERIFICATION.path);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
    setLoading(false);
  };
  return (
    <>
      <AdminForgotPasswordForm onSubmit={onSubmit} t={t} loading={loading} />
    </>
  );
}

export default ForgotPassword;
