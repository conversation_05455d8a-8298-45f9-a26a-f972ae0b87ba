import { Radio } from "antd";
import React from "react";
import "./AddCustomer.scss";

const PlanTabsUsereg = ({
  plans = [],
  handlePlanChange,
  handleDurationChange,
  // handleUpdateSubscription,
  subscriptionDetails,
  setSubscriptionDetails,
  isCorporate,
  expiaryDate,
}) => {
  const itemsData = {
    keys: ["1", "2", "3"],
    labels: ["Translation", "Video Conference", "Bundled"],
    tableHeaders: ["Plan Name", "Plan Type", "Plan Duration", "Expiairy Date"],
    plansType: ["translation", "videoConferencing", "bundled"],
    duration: ["monthly", "quarterly", "halfyearly", "annual", "start_date"],
    durationLabels: [
      "Monthly",
      "Quarterly",
      "Half Yearly",
      "Annual",
      "Custom Date",
    ],
    paymentMethod: ["online", "offline"],
    paymentMethodLabels: ["Online", "Offline"],
    translationPlans: plans
      .filter(
        (plan) => plan.plan_type === "translation" && plan.is_corporate === 0
      )
      .sort((a, b) => a.price - b.price),
    corporateTranslationPlans: plans.filter(
      (plan) => plan.plan_type === "translation" && plan.is_corporate === 1
    ),
    videoConferencingPlans: plans
      .filter(
        (plan) =>
          plan.plan_type === "videoConferencing" && plan.is_corporate === 0
      )
      .sort((a, b) => a.price - b.price),
    corporateVideoConferencingPlans: plans.filter(
      (plan) =>
        plan.plan_type === "videoConferencing" && plan.is_corporate === 1
    ),
    bundledPlans: plans
      .filter((plan) => plan.plan_type === "bundled" && plan.is_corporate === 0)
      .sort((a, b) => a.price - b.price),
    corporateBundledPlans: plans.filter(
      (plan) => plan.plan_type === "bundled" && plan.is_corporate === 1
    ),
  };
  // const [payMethod, setPayMethod] = React.useState(itemsData.paymentMethod[1]);

  const translationPlanOptions = itemsData.translationPlans.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const corporateTranslationPlanOptions =
    itemsData.corporateTranslationPlans.map((plan) => (
      <option key={plan.id} value={plan.id}>
        {`${plan.name}`}
      </option>
    ));

  const videoConferencingPlanOptions = itemsData.videoConferencingPlans.map(
    (plan) => (
      <option key={plan.id} value={plan.id}>
        {`${plan.name}`}
      </option>
    )
  );
  const corporateVideoConferencingPlanOptions =
    itemsData.corporateVideoConferencingPlans.map((plan) => (
      <option key={plan.id} value={plan.id}>
        {`${plan.name}`}
      </option>
    ));

  const bundledPlanOptions = itemsData.bundledPlans.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const corporateBundledPlanOptions = itemsData.corporateBundledPlans.map(
    (plan) => (
      <option key={plan.id} value={plan.id}>
        {`${plan.name}`}
      </option>
    )
  );

  const durationOptions = itemsData.duration.map((d, index) => (
    <option key={d} value={d}>
      {itemsData.durationLabels[index]}
    </option>
  ));

  const paymentMethodOptions = itemsData.paymentMethod.map((method, index) => (
    <Radio key={method} value={method}>
      {itemsData.paymentMethodLabels[index]}
    </Radio>
  ));

  const items = itemsData.keys.map((key, index) => {
    return {
      key,
      label: itemsData.labels[index],
      children: (
        <form
          onSubmit={(event) => {
            event.preventDefault();
            // handleUpdateSubscription();
          }}
          className="choose-subscription"
        >
          <table className="up-modal-form-table">
            <thead>
              <tr>
                {itemsData.tableHeaders.map((header) => (
                  <th key={header}>{header}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="subscription-type">{itemsData.labels[index]}</td>
                <td>
                  <select
                    id="subscription"
                    name="subscription"
                    onChange={handlePlanChange(itemsData.plansType[index])}
                  >
                    <option value="">Select a plan</option>
                    {index === 0
                      ? isCorporate === 0
                        ? translationPlanOptions
                        : corporateTranslationPlanOptions
                      : index === 1
                      ? isCorporate === 0
                        ? videoConferencingPlanOptions
                        : corporateVideoConferencingPlanOptions
                      : isCorporate === 0
                      ? bundledPlanOptions
                      : corporateBundledPlanOptions}
                  </select>
                </td>
                <td>
                  <select
                    id="duration"
                    name="duration"
                    onChange={handleDurationChange}
                  >
                    <option value="">Select a duration</option>
                    {durationOptions}
                  </select>
                </td>
                <td>{expiaryDate}</td>
              </tr>
            </tbody>
          </table>
          <div className="choose-subscription-below">
            {isCorporate === true && (
              <>
                <div className="no-of-liscence">
                  <label htmlFor="no-of-liscence-keys">
                    No. of Liscence Key
                  </label>
                  <select
                    id="no-of-liscence-keys"
                    name="no-of-liscence-keys"
                    onChange={(e) => {
                      setSubscriptionDetails({
                        ...subscriptionDetails,
                        noLicenses: e.target.value,
                      });
                    }}
                    value={subscriptionDetails.noLicenses}
                  >
                    <option value="1">Select no. of keys</option>
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="15">15</option>
                    <option value="20">20</option>
                    <option value="25">25</option>
                  </select>
                </div>
              </>
            )}
            <div className="pay-method">
              <label htmlFor="payment_method">Payment Method</label>
              <Radio.Group
                onChange={(e) => {
                  setSubscriptionDetails({
                    ...subscriptionDetails,
                    paymentMethod: e.target.value,
                  });
                }}
                name="payment_method"
                className="payment-method"
                value={subscriptionDetails.paymentMethod}
              >
                {paymentMethodOptions}
              </Radio.Group>
            </div>
            {isCorporate === 1 && (
              <div className="pay-method">
                <label htmlFor="payment_method">No of Liscences</label>
                <input
                  type="number"
                  id="payment_method"
                  className="form-control"
                  name="payment_method"
                  value={subscriptionDetails.noLicenses}
                  onChange={(e) => {
                    setSubscriptionDetails({
                      ...subscriptionDetails,
                      noLicenses: e.target.value,
                    });
                  }}
                />
              </div>
            )}
          </div>
        </form>
      ),
    };
  });

  return items;
};

export default PlanTabsUsereg;
