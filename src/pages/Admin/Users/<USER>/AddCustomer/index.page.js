import React, { useEffect, useState } from "react";
import moment from "moment";
import { <PERSON><PERSON>, DatePicker, Tabs, Radio } from "antd";
import { Formik, Form } from "formik";
import "./AddCustomer.scss";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { AntSelect, ModalComponent } from "../../../../../components/index";
import {
  AdminManageSubscriptionServices,
  CommonServices,
  UsersServices,
} from "../../../../../services";
import { logger, modalNotification } from "../../../../../utils";
import PlanTabsUsereg from "./PlansTabsUsereg";

export default function AddCustomer() {
  const [countryList, setCountryList] = useState([]);
  const [allSubscriptions, setAllSubscriptions] = useState([]);
  const [stateList, setStateList] = useState([]);
  const [citiesList, setCitiesList] = useState([]);
  const [userData, setUserData] = useState({
    // general user data
    firstName: "",
    lastName: "",
    dob: "",
    email: "",
    countryCode: "",
    phoneNumber: "",
    password: "password",
    isCorporate: 0,

    // corporate user data
    companyName: "",
    gstNumber: "",
    panNumber: "",
    address: "",
    country: "",
    countryId: null,
    state: "",
    stateId: "",
    city: "",
    cityId: "",
    postalCode: "",
    isAdmin: 1,
  });

  const { executeRecaptcha } = useGoogleReCaptcha();

  const [subscriptionDetails, setSubscriptionDetails] = useState({
    subscriptionId: "",
    userId: "",
    planInterval: "",
    startDate: "",
    paymentMethod: "offline",
    noLicenses: 1,
    isTiralUser: 1,
  });

  const [customDate, setCustomDate] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState("");
  const [expiaryDate, setExpiaryDate] = useState("");
  const [selectedCustomDateDuration, setSelectedCustomDateDuration] = useState("");

  const handleCustomDateDurationChange = (event) => {
    setSelectedCustomDateDuration(event.target.value);
  };

  const calculateExpiaryDate = (duration) => {
    let subscriptionExpiryDate = moment();
    if (duration === "monthly") {
      subscriptionExpiryDate = moment().add(1, "months");
    } else if (duration === "quarterly") {
      subscriptionExpiryDate = moment().add(3, "months");
    } else if (duration === "halfyearly") {
      subscriptionExpiryDate = moment().add(6, "months");
    } else if (duration === "annual") {
      subscriptionExpiryDate = moment().add(1, "years");
    }
    return subscriptionExpiryDate.format("DD-MM-YYYY");
  };

  useEffect(() => {
    if (selectedDuration) {
      setExpiaryDate(calculateExpiaryDate(selectedDuration));
    }
  }, [selectedDuration]);

  useEffect(() => {
      if (selectedCustomDateDuration) {
        let subscriptionExpiryDate = moment(selectedDate);
        if (selectedCustomDateDuration === "monthly") {
          subscriptionExpiryDate = moment(selectedDate).add(1, "months");
        } else if (selectedCustomDateDuration === "quarterly") {
          subscriptionExpiryDate = moment(selectedDate).add(3, "months");
        } else if (selectedCustomDateDuration === "halfyearly") {
          subscriptionExpiryDate = moment(selectedDate).add(6, "months");
        } else if (selectedCustomDateDuration === "annual") {
          subscriptionExpiryDate = moment(selectedDate).add(1, "years");
        }
        setExpiaryDate(subscriptionExpiryDate.format("DD-MM-YYYY"));
      }
    }, [selectedCustomDateDuration]);

  const getAllSubscriptions = async () => {
    try {
      const queryParams = {
        limit: 50,
        status: "active",
        typeList: "checked",
      };
      const res =
        await AdminManageSubscriptionServices.subscriptionListingService({
          queryParams,
        });

      const { success, data, message } = res;
      if (success === 1) {
        setAllSubscriptions(data?.rows);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
  };

  useEffect(() => {
    getAllSubscriptions();
  }, []);

  useEffect(() => {
    // fetch countries
    const fetchCountries = async () => {
      try {
        const res = await CommonServices.countries();
        if (res.success === 1) {
          setCountryList(res.data.rows);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchCountries();
  }, []);

  const getStateList = async (id) => {
    try {
      setStateList([]);
      setCitiesList([]);
      let queryParams = {
        country_id: id,
      };
      let res = await CommonServices.stateList({ queryParams });
      const { data, success, message } = res;
      if (success === 1) {
        setStateList(data?.rows);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
  };

  const getCitiesList = async (countryId, stateId) => {
    try {
      setCitiesList([]);
      let queryParams = {
        country_id: countryId,
        state_id: stateId,
      };
      let res = await CommonServices.citiesList({ queryParams });
      const { data, success, message } = res;
      if (success === 1) {
        setCitiesList(data?.rows);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
  };

  // useEffect(() => {
  //   if (userData?.countryCode) getStateList(userData?.countryCode);
  //   if (userData?.country_code && userData?.state_code)
  //     getCitiesList(userData?.country_code, userData?.state_code);
  // }, []);

  const handlePlanChange = () => (subscriptionId) => {
    setSubscriptionDetails({
      ...subscriptionDetails,
      subscriptionId: subscriptionId.target.value,
    });
  };

  const handleDurationChange = (event) => {
    setSelectedDuration(event.target.value);
    setSubscriptionDetails({
      ...subscriptionDetails,
      planInterval: event.target.value,
    });
    calculateExpiaryDate(selectedDuration);
    if (event.target.value === "start_date") {
      setCustomDate(true);
    } else {
      setCustomDate(false);
    }
  };

  const handlePlanUpdate = async (payload) => {
    const token = await executeRecaptcha("admin_update_user_subscription");
    payload.recaptcha_token = token;
    try {
      const res = await UsersServices.updateSubscriptionPlanService(payload);
      if (res.success === 1) {
        modalNotification({
          type: "success",
          message: res.message,
        });

        // reset the subscription details
        setSubscriptionDetails({
          subscriptionId: "",
          userId: "",
          planInterval: "",
          startDate: "",
          paymentMethod: "",
          noLicenses: 1,
          isTiralUser: 1,
        });

        // window.location.reload();
      } else {
        modalNotification({
          type: "error",
          message: res.message,
        });
      }
    } catch (error) {
      logger(error);
    }
  };

  const handleUserRegistration = async (resetForm) => {
    if (
      !subscriptionDetails.subscriptionId ||
      !subscriptionDetails.planInterval
    ) {
      modalNotification({
        type: "error",
        message: "Please select a subscription plan and duration",
      });
      return;
    }
    try {
      const token = await executeRecaptcha("admin_add_customer");
      let payload = {
        // general user data
        full_name: userData.firstName.concat(" ", userData.lastName),
        dob: userData.dob,
        email: userData.email,
        country_code: userData.countryCode.toString(),
        phone_number: userData.phoneNumber,
        is_corporate: userData.isCorporate === 1,
        password: "password",

        // corporate user data
        business_name: userData.companyName,
        country_id: userData.countryId,
        state_id: userData.stateId,
        city_id: userData.cityId,
        gst_number: userData.gstNumber,
        pan_number: userData.panNumber,
        address: userData.address,
        postal_code: userData.postalCode,

        recaptcha_token: token
      };
      if (userData.isCorporate === 0) {
        delete payload.business_name;
        delete payload.country_id;
        delete payload.state_id;
        delete payload.city_id;
        delete payload.gst_number;
        delete payload.pan_number;
        delete payload.address;
        delete payload.postal_code;
      }
      if (userData.dob === "") {
        delete payload.dob;
      }
      if (userData.phoneNumber === "") {
        delete payload.phone_number;
        delete payload.country_code;
      }
      if(userData.countryId === null){
        delete payload.country_id;
        delete payload.state_id;
        delete payload.city_id;
      }
      if(userData.address === ""){
        delete payload.address;
      }
      if(userData.postalCode === ""){
        delete payload.postal_code;
      }
      if(userData.gstNumber === ""){
        delete payload.gst_number;
      }
      if(userData.panNumber === ""){
        delete payload.pan_number;
      }
      const res = await AdminManageSubscriptionServices.registerUserService(
        payload
      );
      if (res.success === 1) {
        const payload2 = {
          user_id: res.data.user_id,
          subscription_id: Number(subscriptionDetails.subscriptionId),
          plan_interval: subscriptionDetails.planInterval,
          no_licenses: subscriptionDetails.noLicenses,
          is_tiral_user: subscriptionDetails.isTiralUser,
          is_corporate: userData.isCorporate === 1,
          business_name: userData.companyName,
        };
        if(userData.isCorporate === 0){
          delete payload2.business_name;
        }
        modalNotification({
          type: "success",
          message: res.message,
        });

        handlePlanUpdate(payload2);

        resetForm();
      } else {
        modalNotification({
          type: "error",
          message: res.message,
        });
      }
    } catch (error) {
      logger(error);
    }
  };

  const onDateChange = (date) => {
    setSelectedDate(date);
  };

  return (
    <div className="add-cust">
      <h1 className="add-cust-heading">Add Customer</h1>
      <Formik
        initialValues={{
          fname: "",
          lname: "",
          dob: "",
          email: "",
          countryCode: "",
          phone: "",
          isCorporate: 0,
          companyName: "",
          gstNumber: "",
          panNumber: "",
          address: "",
          country: "",
          state: "",
          city: "",
          no_licenses: "",
          age: "",
          password: "password",
        }}
        onSubmit={(e, { resetForm }) => {
          handleUserRegistration(resetForm);
          resetForm();
          setUserData({});
        }}
      >
        {({ values, setFieldValue, handleChange }) => (
          <Form className="add-cust-form">
            <div className="add-cust-form-row">
              <div className="form-group">
                <label htmlFor="name">
                  First Name<span className="danger">*</span>
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="fname"
                  name="fname"
                  onChange={(e) => {
                    handleChange(e);
                    setFieldValue("fname", e.target.value);
                    setUserData({ ...userData, firstName: e.target.value });
                  }}
                  value={values.fname}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="name">
                  Last Name<span className="danger">*</span>
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="lname"
                  name="lname"
                  onChange={(e) => {
                    handleChange(e);
                    setFieldValue("lname", e.target.value);
                    setUserData({ ...userData, lastName: e.target.value });
                  }}
                  value={values.lname}
                  required
                />
              </div>
            </div>

            <div className="add-cust-form-row">
              <div className="form-group">
                <label htmlFor="age">Date of Birth</label>
                <DatePicker
                  className="form-control"
                  id="dob"
                  name="dob"
                  onChange={(date, dateString) => {
                    setFieldValue("dob", dateString);
                    setUserData({
                      ...userData,
                      dob: dateString.replaceAll("-", "/"),
                    });
                  }}
                />
              </div>

              <div className="form-group">
                <label htmlFor="email">
                  Email<span className="danger">*</span>
                </label>
                <input
                  type="email"
                  className="form-control"
                  id="email"
                  name="email"
                  onChange={(e) => {
                    handleChange(e);
                    setFieldValue("email", e.target.value);
                    setUserData({ ...userData, email: e.target.value });
                  }}
                  value={values.email}
                  required
                />
              </div>
            </div>

            <div className="add-cust-form-row">
              <div className="form-group">
                <label htmlFor="phone">Phone</label>
                <div className="form-group-phone">
                  <AntSelect
                    size="medium"
                    id="status"
                    extraClassName="form-control"
                    className="country-code"
                    overlayClassName="country-code-dropdown"
                    name="countryCode"
                    placeholder=""
                    showSearch
                    arrayOfData={countryList}
                    value={`${userData?.countryCode || ""}`} // added later
                    // onSelect={(value) => {
                    //   setFieldValue("country_code", value);
                    //   setUserData({ ...userData, country_code: value.split("+")[1] });
                    // }}
                    onSelect={(value) => {
                      // const selectedCountry = countryList.find(
                      //   (item) => `+${item.phone_code}` === value
                      // );
                      setFieldValue("countryCode", value);
                      setUserData({
                        ...userData,
                        countryCode: value.split("+")[1],
                        // country: selectedCountry?.name || "", // Update country as well
                      });
                      // setFieldValue("country", selectedCountry?.name || ""); // Update the form country field
                    }}
                  >
                    {countryList?.length > 0 &&
                      countryList.map((item, key) => (
                        <option
                          key={key}
                          value={`+${item?.phone_code}`}
                          className="country-code-options"
                        >
                          {`+${item?.phone_code}`}
                        </option>
                      ))}
                  </AntSelect>
                  <input
                    type="number"
                    className="form-control"
                    id="phone"
                    name="phone"
                    onChange={(e) => {
                      handleChange(e);
                      setFieldValue("phone", e.target.value);
                      setUserData({
                        ...userData,
                        phoneNumber: e.target.value,
                      });
                    }}
                    value={values.phone}
                  />
                </div>
              </div>

              {/* <div className="form-group">
                <div className="form-group-password">
                  <div className="form-group-password-options">
                    <label htmlFor="password">Static OTP</label> */}
              {/* Random Password Button */}
              {/* <Button
                      onClick={() => {
                        const password = Math.random().toString(36).slice(-8);
                        setFieldValue("password", password);
                        setUserData({ ...userData, password });
                      }}
                      type="text"
                    >
                      Generate Password
                    </Button> */}
              {/* Random Static OTP Button */}
              {/* <Button
                      onClick={() => {
                        // generate a random 6 digit otp
                        const password = Math.floor(100000 + Math.random() * 900000);
                        setFieldValue("password", password);
                        setUserData({ ...userData, password });
                      }}
                      type="text"
                    >
                      Generate OTP
                    </Button>
                  </div>
                  <AForm.Item
                    name="password"
                    label="Password"
                    rules={[
                      {
                        required: true,
                        message: "Please input your password!",
                      },
                    ]}
                  >
                    <Input.Password
                      value={values.password}
                      onChange={(e) => {
                        handleChange("password");
                        setFieldValue("password", e.target.value);
                        setUserData({ ...userData, password: e.target.value });
                      }}
                      className="form-control pwd-input"
                    /> */}
              {/* Copy Button */}
              {/* <Button
                      onClick={() => {
                        navigator.clipboard.writeText(values.password);
                      }}
                      type="text"
                    >
                      <CopyOutlined />
                    </Button>
                  </AForm.Item>
                </div>
              </div> */}
              <div className="form-group">
                <div className="form-group-is-corporate">
                  <label htmlFor="isCorporate">Is Corporate?</label>
                  <Radio.Group
                    name="isCorporate"
                    onChange={(e) => {
                      setFieldValue("isCorporate", e.target.value);
                      setUserData({ ...userData, isCorporate: e.target.value });
                    }}
                    className="radio-group"
                    id="isCorporate"
                    defaultValue={0}
                    value={values.isCorporate}
                  >
                    <Radio value={1}>Yes</Radio>
                    <Radio value={0}>No</Radio>
                  </Radio.Group>
                </div>
              </div>
            </div>

            {/* <div className="add-cust-form-row">
            </div> */}

            {userData.isCorporate === 1 && (
              <>
                <div className="add-cust-form-row">
                  <div className="form-group">
                    <label htmlFor="companyName">
                      Company Name<span className="danger">*</span>
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      id="companyName"
                      name="companyName"
                      onChange={(e) => {
                        handleChange(e);
                        setFieldValue("companyName", e.target.value);
                        setUserData({
                          ...userData,
                          companyName: e.target.value,
                        });
                      }}
                      value={values.companyName}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>GST Number</label>
                    <input
                      type="text"
                      className="form-control"
                      id="gstNumber"
                      name="gstNumber"
                      onChange={(e) => {
                        handleChange(e);
                        setFieldValue("gstNumber", e.target.value);
                        setUserData({
                          ...userData,
                          gstNumber: e.target.value,
                        });
                      }}
                      value={values.gstNumber}
                    />
                  </div>
                </div>

                <div className="add-cust-form-row">
                  <div className="form-group">
                    <label>PAN Number</label>
                    <input
                      type="text"
                      className="form-control"
                      id="panNumber"
                      name="panNumber"
                      onChange={(e) => {
                        handleChange(e);
                        setFieldValue("panNumber", e.target.value);
                        setUserData({
                          ...userData,
                          panNumber: e.target.value,
                        });
                      }}
                      value={values.panNumber}
                    />
                  </div>
                  <div className="form-group">
                    <label>Address</label>
                    <input
                      type="text"
                      className="form-control"
                      id="address"
                      name="address"
                      onChange={(e) => {
                        handleChange(e);
                        setFieldValue("address", e.target.value);
                        setUserData({ ...userData, address: e.target.value });
                      }}
                      value={values.address}
                    />
                  </div>
                </div>

                <div className="add-cust-form-row">
                  <div className="form-group">
                    <label>Country</label>
                    <AntSelect
                      size="medium"
                      id="status"
                      extraClassName="form-control"
                      className="country-code"
                      overlayClassName="country-code-dropdown"
                      name="country"
                      placeholder="Select Country"
                      showSearch
                      arrayOfData={countryList}
                      value={userData?.country || ""}
                      onSelect={(value) => {
                        const tempCountryId = countryList.find(
                          (item) => item.name === value
                        )?.id;
                        setFieldValue("country", value);
                        setUserData({
                          ...userData,
                          country: value,
                          countryId: tempCountryId,
                        });
                        getStateList(tempCountryId);
                      }}
                    >
                      {countryList?.length > 0 &&
                        countryList.map((item, key) => (
                          <option
                            key={key}
                            value={item?.name}
                            className="country-code-options"
                          >
                            {item?.name}
                          </option>
                        ))}
                    </AntSelect>
                  </div>
                  <div className="form-group">
                    <label>State</label>
                    <AntSelect
                      size="medium"
                      id="status"
                      extraClassName="form-control"
                      className="country-code"
                      overlayClassName="country-code-dropdown"
                      name="state"
                      placeholder="Select State"
                      showSearch
                      arrayOfData={stateList}
                      value={userData?.state || ""}
                      onSelect={(value) => {
                        const tempStateId = stateList.find(
                          (item) => item.name === value
                        )?.id;
                        setFieldValue("state", value);
                        setUserData({
                          ...userData,
                          state: value,
                          stateId: tempStateId,
                        });
                        getCitiesList(userData?.countryCode, tempStateId);
                      }}
                    >
                      {stateList?.length > 0 &&
                        stateList.map((item, key) => (
                          <option
                            key={key}
                            value={item?.name}
                            className="country-code-options"
                          >
                            {item?.name}
                          </option>
                        ))}
                    </AntSelect>
                  </div>
                </div>

                <div className="add-cust-form-row">
                  <div className="form-group">
                    <label>City</label>
                    <AntSelect
                      size="medium"
                      id="status"
                      extraClassName="form-control"
                      className="country-code"
                      overlayClassName="country-code-dropdown"
                      name="city"
                      placeholder="Select City"
                      showSearch
                      arrayOfData={citiesList}
                      value={userData?.city || ""}
                      onSelect={(value) => {
                        const tempCityId = citiesList.find(
                          (item) => item.city_name === value
                        )?.id;
                        setFieldValue("city", value);
                        setUserData({
                          ...userData,
                          city: value,
                          cityId: tempCityId,
                        });
                      }}
                    >
                      {citiesList?.length > 0 &&
                        citiesList.map((item, key) => (
                          <option
                            key={key}
                            value={item?.city_name}
                            className="country-code-options"
                          >
                            {item?.city_name}
                          </option>
                        ))}
                    </AntSelect>
                  </div>
                  <div className="form-group">
                    <label>Postal Code</label>
                    <input
                      type="text"
                      className="form-control"
                      id="postalCode"
                      name="postalCode"
                      onChange={(e) => {
                        handleChange(e);
                        setFieldValue("postalCode", e.target.value);
                        setUserData({
                          ...userData,
                          postalCode: e.target.value,
                        });
                      }}
                      value={values.postalCode}
                    />
                  </div>
                </div>

                {/* <div className="add-cust-form-row">
                  <div className="form-group-is-corporate">
                    <label>Is Admin?</label>
                    <Radio.Group
                      name="isTrialUser"
                      onChange={(e) => {
                        setSubscriptionDetails({
                          ...userData,
                          isAdmin: e.target.value,
                        });
                      }}
                      className="radio-group"
                      value={userData.isAdmin}
                    >
                      <Radio value={1}>Yes</Radio>
                      <Radio value={0}>No</Radio>
                    </Radio.Group>
                  </div>
                </div> */}
              </>
            )}

            <div className="add-cust-form-row">
              <Tabs
                className="add-cust-form-tabs"
                defaultActiveKey="1"
                items={PlanTabsUsereg({
                  plans: allSubscriptions,
                  handlePlanChange,
                  handleDurationChange,
                  // setPaymentMethod,
                  subscriptionDetails,
                  setSubscriptionDetails,
                  // selectedDate,
                  // setSelectedDate,
                  // selectedDuration,
                  isCorporate: userData.isCorporate,
                  expiaryDate,
                })}
              />
            </div>

            {/* {userData.isCorporate === 1 && (
              <div className="add-cust-form-row">
                <div className="form-group">
                  <label>No. Of Liscenece Keys</label>
                  <AntSelect
                    size="medium"
                    id="status"
                    extraClassName="form-control"
                    className="country-code"
                    overlayClassName="country-code-dropdown"
                    name="no_licenses"
                    placeholder="Select No. Of Liscenece Keys"
                    showSearch
                    arrayOfData={[5, 10, 15, 20, 25]}
                    value={userData?.noLicenses || ""}
                    onSelect={(value) => {
                      setFieldValue("no_licenses", value);
                      setUserData({
                        ...subscriptionDetails,
                        noLicenses: value,
                      });
                    }}
                  >
                    {[5, 10, 15, 20, 25].map((item, key) => (
                      <option
                        key={key}
                        value={item}
                        className="country-code-options"
                      >
                        {item}
                      </option>
                    ))}
                  </AntSelect>
                </div>
              </div>
            )} */}

            <Button
              type="primary"
              htmlType="submit"
              className="btn btn-primary guest-user-submit-btn"
            >
              Submit
            </Button>
          </Form>
        )}
      </Formik>
      {customDate && (
        <ModalComponent
          title="Choose a Date"
          visible={customDate}
          onCancel={() => setCustomDate(false)}
          zIndex={1100}
          backdrop
          show={customDate}
          onHandleCancel={() => setCustomDate(false)}
          extraClassName="custom-date-modal"
          onOk={() => {
            setCustomDate(false);
          }}
        >
          <div className="custom-date-modal-choose-date">
            <span>
              Choose a Starting Date:&nbsp;
            </span>
            <DatePicker 
              onChange={onDateChange}
              getPopupContainer={trigger => trigger.parentNode}
              defaultValue={selectedDate}
            />
          </div>
          <div className="custom-date-modal-choose-date">
            <span>Choose a Plan Duration<span className="danger">*</span>:</span>
            <select
              id="duration"
              name="duration"
              onChange={handleCustomDateDurationChange}
              className="custom-date-modal-select"
            >
              <option value="">Select a plan</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="halfyearly">Half Yearly</option>
              <option value="annual">Annual</option>
            </select>
          </div>
          <Button
            type="primary"
            onClick={() => setCustomDate(false)}
            className="up-modal-form-button"
          >
            Done
          </Button>
        </ModalComponent>
      )}
    </div>
  );
}
