@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

$font: "Inter";

.add-cust {
  display: flex;
  flex-direction: column;
  align-items: center;
  &-heading {
    font-family: $font;
    font-size: 32px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 20px;
  }
  &-form {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 60%;
    min-width: 500px;
    position: relative;
    @media screen and (max-width: 540px) {
      gap: 1rem;
    }
    &-row {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: space-between;
      @media screen and (max-width: 540px) {
        flex-direction: column;
        align-items: center;
        gap: 0;
      }
      .form-group {
        width: 45%;
        .ant-select-auto-complete {
          padding: 0 !important;
        }
        &-password {
          display: flex;
          flex-direction: column;
          &-options {
            display: flex;
            justify-content: space-between;
            label{
              margin: 0;
              font-size: 14px;
            }
            button{
              padding: 0;
              height: auto;
            }
          }
          .ant-form-item-label {
            display: none;
          }
          .ant-form-item-control-input-content {
            display: flex;
            position: relative;
            .ant-input-password {
              border-right: none;
              border-radius: 4px 0 0 4px;
              display: flex;
            }
            button{
              height: auto;
            }
            &:hover {
              button {
                border: 1px solid #559bfb;
                border-left: none;
              }
            }
            button {
              padding: 0;
              display: flex;
              width: 10%;
              align-items: center;
              justify-content: center;
              background-color: white;
              border: 1px solid #d9d9d9;
              border-left: none;
              border-radius: 0 4px 4px 0;
              span {
                color: #8091a7;
              }
              &:hover {
                span {
                  color: #1f2b3a;
                }
              }
            }
          }
        }
        &-is-corporate{
          display: flex;
          flex-direction: column;
        }
        &-phone {
          display: flex;
          .ant-form-item {
            width: 40%;
            .ant-select-selection-search-input {
              padding: 0 8px !important;
            }
            .ant-select-selection-item {
              padding: 8px !important;
            }
          }
        }
      }
    }
    &-tabs{
      width: 100%;
      .up-modal-form-table{
        width: 100%;
      }
    }
  }
}
.country-code-options {
  padding: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
  .ant-select-item-option-content{
    text-align: center;
  }
}
.guest-user-submit-btn{
  margin-top: 1rem;
}
.danger{
  color: rgb(237, 4, 4);
}
.select-plan-duration{
  display: flex;
  flex-direction: column;
  justify-content: center;
}