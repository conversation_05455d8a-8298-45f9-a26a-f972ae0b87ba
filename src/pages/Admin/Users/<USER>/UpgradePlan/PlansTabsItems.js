import { InputNumber, Radio } from "antd";
import React, { useEffect } from "react";

const PlanTabsItems = ({
  plans = [],
  handlePlanChange,
  handleDurationChange,
  handleUpdateSubscription,
  // customDate,
  // setCustomDate,
  // paymentMethod,
  setPaymentMethod,
  // selectedDuration,
  expiaryDate,
  isCorporate,
  setNoLicenses,
  noLicenses,
  setBusinessName,
  selectedcustomerdata,
}) => {
  const itemsData = {
    keys: ["1", "2", "3"],
    labels: ["Translation", "Video Conference", "Bundled"],
    tableHeaders: ["Plan Name", "Plan Type", "Plan Duration", "Expiary Date"],
    plansType: ["translation", "videoConferencing", "bundled"],
    duration: ["monthly", "quarterly", "halfyearly", "annual", "start_date"],
    durationLabels: ["Monthly", "Quarterly", "Half Yearly", "Annual", "Custom Date"],
    paymentMethod: ["online", "offline"],
    paymentMethodLabels: ["Online", "Offline"],
    translationPlans: plans.filter((plan) => plan.plan_type === "translation" && plan.is_corporate === 0).sort((a, b) => a.price - b.price),
    translationPlansCorporate: plans.filter((plan) => plan.plan_type === "translation" && plan.is_corporate === 1).sort((a, b) => a.price - b.price),
    videoConferencingPlans: plans.filter((plan) => plan.plan_type === "videoConferencing" && plan.is_corporate === 0).sort((a, b) => a.price - b.price),
    videoConferencingPlansCorporate: plans.filter((plan) => plan.plan_type === "videoConferencing" && plan.is_corporate === 1).sort((a, b) => a.price - b.price),
    bundledPlans: plans.filter((plan) => plan.plan_type === "bundled" && plan.is_corporate === 0).sort((a, b) => a.price - b.price),
    bundledPlansCorporate: plans.filter((plan) => plan.plan_type === "bundled" && plan.is_corporate === 1).sort((a, b) => a.price - b.price),
  }
  const [payMethod, setPayMethod] = React.useState(itemsData.paymentMethod[0]);

  const translationPlanOptions = itemsData.translationPlans.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const translationPlanOptionsCorporate = itemsData.translationPlansCorporate.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const videoConferencingPlanOptions = itemsData.videoConferencingPlans.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const videoConferencingPlanOptionsCorporate = itemsData.videoConferencingPlansCorporate.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const bundledPlanOptions = itemsData.bundledPlans.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const bundledPlanOptionsCorporate = itemsData.bundledPlansCorporate.map((plan) => (
    <option key={plan.id} value={plan.id}>
      {`${plan.name}`}
    </option>
  ));
  const durationOptions = itemsData.duration.map((d, index) => (
    <option key={d} value={d}>
      {itemsData.durationLabels[index]}
    </option>
  ));
  const paymentMethodOptions = itemsData.paymentMethod.map((method, index) => (
    <Radio key={method} value={method}>
      {itemsData.paymentMethodLabels[index]}
    </Radio>
  ));

  useEffect(() => {
    setPaymentMethod(payMethod);
  }, [payMethod]);

  const items = itemsData.keys.map((key, index) => {
    return {
      key,
      label: itemsData.labels[index],
      children: (
        <form
          onSubmit={(event) => {
            event.preventDefault();
            handleUpdateSubscription();
          }}
          className="choose-subscription"
        >
          <table className="up-modal-form-table">
            <thead>
              <tr>
                {itemsData.tableHeaders.map((header) => (
                  <th key={header}>{header}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="subscription-type">{itemsData.labels[index]}</td>
                <td>
                  <select
                    id="subscription"
                    name="subscription"
                    onChange={handlePlanChange(itemsData.plansType[index])}
                  >
                    <option value="">Select a plan</option>
                    {/* {index === 0 ? translationPlanOptions : index === 1 ? videoConferencingPlanOptions : bundledPlanOptions} */}
                    {index === 0 ? (
                      isCorporate === false ? translationPlanOptions : translationPlanOptionsCorporate
                    ) : index === 1 ? (
                      isCorporate === false ? videoConferencingPlanOptions : videoConferencingPlanOptionsCorporate
                    ) : (
                      isCorporate === false ? bundledPlanOptions : bundledPlanOptionsCorporate
                    )}
                  </select>
                </td>
                <td>
                  <select
                    id="duration"
                    name="duration"
                    onChange={handleDurationChange}
                  >
                    <option value="">Select a duration</option>
                    {durationOptions}
                  </select>
                </td>
                <td>
                  {expiaryDate}
                </td>
              </tr>
            </tbody>
          </table>
          <div className="choose-subscription-below">
            {isCorporate === true && (
              <>
                <div className="no-of-liscence">
                  <label htmlFor="no-of-liscence-keys">No. of Liscence Key</label>
                  <InputNumber
                    id="no-of-liscence-keys" 
                    name="no-of-liscence-keys"
                    onChange={setNoLicenses}
                    value={noLicenses}
                    // defaultValue={selectedcustomerdata?.customerIsCorporate?.no_of_licenses}
                    min={1}
                    max={999}
                    style={{ width: "100%" }}
                    className="no-of-liscence-keys"
                    placeholder="Enter no. of liscence keys"
                  />
                  {/* <select 
                    id="no-of-liscence-keys" 
                    name="no-of-liscence-keys"
                    onChange={(e) => setNoLicenses(e.target.value)}
                    defaultValue={selectedcustomerdata?.customerIsCorporate?.no_of_licenses}
                  >
                    <option value="1">Select no. of keys</option>
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="15">15</option>
                    <option value="20">20</option>
                    <option value="25">25</option>
                  </select> */}
                </div>
                <div className="business-name">
                  <label htmlFor="business-name">Business Name</label>
                  <input 
                    type="text" 
                    id="business-name" 
                    name="business-name"
                    onChange={(e) => setBusinessName(e.target.value)}
                    defaultValue={isCorporate === true ? selectedcustomerdata?.customerIsCorporate?.organization_name : ""}
                  />
                </div>
              </>
            )}
            <div className="pay-method">
              <label htmlFor="payment_method">Payment Method</label>
              <Radio.Group
                onChange={(e) => setPayMethod(e.target.value)}
                name="payment_method"
                className="payment-method"
                value={payMethod}
              >
                {paymentMethodOptions}
              </Radio.Group>
            </div>
          </div>
        </form>
      ),
    };
  });

  return items;
};

export default PlanTabsItems;
