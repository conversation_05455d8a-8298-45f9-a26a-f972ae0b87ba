import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, DatePicker, Radio, Tabs } from "antd";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { success } from "toastr";
import moment from "moment";
import PlansTabsItems from "./PlansTabsItems";
import { UsersServices } from "../../../../../services";
import "./UpgradePlan.scss";
import ModalComponent from "../../../../../components/UiElement/Modal";

export default function UpgradePlan({
  selectedcustomerdata,
  allsubscriptions,
  setupgradeplanmodal,
}) {
  const [plans, setPlans] = useState([]); // State to store the fetched plans
  const [selectedPlan, setSelectedPlan] = useState("");
  const [selectedDuration, setSelectedDuration] = useState("");
  const [selectedCustomDateDuration, setSelectedCustomDateDuration] = useState("");
  const [customDate, setCustomDate] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState("online" || "");
  const [expiaryDate, setExpiaryDate] = useState("");
  const [isCorporate, setIsCorporate] = useState(!!selectedcustomerdata?.customerIsCorporate);
  const [noLicenses, setNoLicenses] = useState(1);
  const [businessName, setBusinessName] = useState("");
  const { executeRecaptcha } = useGoogleReCaptcha();
  const matchedPlans = selectedcustomerdata?.customerCurrentPlanType
    .map((customerPlan) => {
      return allsubscriptions.find(
        (plan) => plan.id === Number(customerPlan.subscription_id)
      );
    })
    .filter((plan) => plan !== undefined);

  useEffect(() => {
    setPlans(allsubscriptions);
  }, []);

  const handlePlanChange = () => (event) => {
    setSelectedPlan(event.target.value);
  };

  const updateUserSubscription = async (
    subscriptionId,
    usersId,
    planInterval,
    startDate,
    payMethod,
    nosLicenses,
    corporateUser,
    businessN,
  ) => {
    try {
      const token = await executeRecaptcha("admin_update_user_subscription");
      const res = await UsersServices.updateSubscriptionPlanService({
        subscription_id: subscriptionId,
        user_id: usersId,
        plan_interval: planInterval,
        start_date: startDate,
        payment_method: payMethod,
        no_licenses: nosLicenses,
        is_corporate: corporateUser,
        business_name: businessN,
        recaptcha_token: token
      });
      setupgradeplanmodal(false);
      success("Plan updated successfully");
      return res;
    } catch (error) {
      console.error(error);
    }
  };

  const handleUpdateSubscription = async () => {
    const data = {
      subscriptionId: Number(selectedPlan),
      usersId: selectedcustomerdata?.customerId,
      plan_interval: selectedDuration,
      start_date: selectedDate,
      payment_method: paymentMethod,
      no_licenses: String(noLicenses),
      is_corporate: isCorporate,
      business_name: businessName,
    };

    await updateUserSubscription(
      data.subscriptionId,
      data.usersId,
      data.plan_interval,
      data.start_date,
      data.payment_method,
      data.no_licenses,
      data.is_corporate,
      data.business_name
    );
  };

  const onDateChange = (date) => {
    setSelectedDate(date);
  };

  const calculateExpiaryDate = (duration) => {
    let subscriptionExpiryDate = moment();
    if (duration === "monthly") {
      subscriptionExpiryDate = moment().add(1, "months");
    } else if (duration === "quarterly") {
      subscriptionExpiryDate = moment().add(3, "months");
    } else if (duration === "halfyearly") {
      subscriptionExpiryDate = moment().add(6, "months");
    } else if (duration === "annual") {
      subscriptionExpiryDate = moment().add(1, "years");
    }
    return subscriptionExpiryDate.format("DD-MM-YYYY");
  }

  useEffect(() => {
    if (selectedDuration) {
      setExpiaryDate(calculateExpiaryDate(selectedDuration));
    }
  }, [selectedDuration]);

  const handleDurationChange = (event) => {
    setSelectedDuration(event.target.value);
    calculateExpiaryDate(selectedDuration);
    if (event.target.value === "start_date") {
      setCustomDate(true);
    } else {
      setCustomDate(false);
    }
  };

  const handleCustomDateDurationChange = (event) => {
    setSelectedCustomDateDuration(event.target.value);
  };

  useEffect(() => {
    if (selectedCustomDateDuration) {
      let subscriptionExpiryDate = moment(selectedDate);
      if (selectedCustomDateDuration === "monthly") {
        subscriptionExpiryDate = moment(selectedDate).add(1, "months");
      } else if (selectedCustomDateDuration === "quarterly") {
        subscriptionExpiryDate = moment(selectedDate).add(3, "months");
      } else if (selectedCustomDateDuration === "halfyearly") {
        subscriptionExpiryDate = moment(selectedDate).add(6, "months");
      } else if (selectedCustomDateDuration === "annual") {
        subscriptionExpiryDate = moment(selectedDate).add(1, "years");
      }
      setExpiaryDate(subscriptionExpiryDate.format("DD-MM-YYYY"));
    }
  }, [selectedCustomDateDuration]);

  return (
    <div className="up-modal">
      <div className="up-modal-details-outer">
        <div className="up-modal-details">
          <div>
            <p>
              <strong>Name:</strong> {selectedcustomerdata?.customerName}
            </p>
            <p>
              <strong>Email:</strong> {selectedcustomerdata?.customerEmail}
            </p>
          </div>
          <div>
          <p>
            <strong>Phone:</strong> {selectedcustomerdata?.customerPhone}
          </p>
          <p>
            <strong>Is Corporate:</strong>
              <Radio.Group
              onChange={(e) => setIsCorporate(e.target.value)}
              name="payment_method"
              className="payment-method"
              value={isCorporate}
              // eslint-disable-next-line react/jsx-boolean-value
              // defaultValue={selectedcustomerdata?.isCorporate === null ? false: true}
              // defaultChecked={selectedcustomerdata?.isCorporate === null ? false: true}
            >
              {/* eslint-disable-next-line react/jsx-boolean-value */}
              <Radio value={true}>Yes</Radio>
              <Radio value={false}>No</Radio>
            </Radio.Group>
          </p>
          </div>
        </div>
        <div className="up-modal-details-table">
          <table style={{width: "100%"}}>
            <tr>
              <th>S.No.</th>
              <th>Plan Type</th>
              <th>Plan Name</th>
              {isCorporate === true && <th>No. of Licenses</th>}
              <th>Amount Paid</th>
              <th>Plan Validity</th>
              <th>Status</th>
            </tr>
            {selectedcustomerdata?.customerCurrentPlan.split(", ").map((plan, index) => (
              <tr key={index}>
                <td>{index + 1}.</td>
                <td>{plan}</td>
                <td>{matchedPlans[index]?.name}</td>
                {isCorporate && <td>{selectedcustomerdata?.customerCurrentPlanType[index]?.no_licenses}</td>}
                <td>
                  {selectedcustomerdata?.customerCurrentPlanType[index]?.payment_method === "online"
                    ? `₹ ${matchedPlans[index]?.price || "0"}`
                    : "₹ 0"}
                </td>
                <td>
                  {selectedcustomerdata?.customerCurrentPlanType[index]?.expires_on}
                </td>
                <td>
                  {selectedcustomerdata?.customerCurrentPlanType[index]?.status === "active" ? 
                    <span className="active">Active</span> : 
                    <span className="inactive">Inactive</span>}
                </td>
              </tr>
            ))}
          </table>
        </div>
      </div>

      {/* Select Plan */}
      <p className="up-modal-choose-plan">Choose a Subscription plan:</p>
      <form
        onSubmit={(event) => {
          event.preventDefault();
          handleUpdateSubscription();
        }}
        className="up-modal-form"
      >
        <Tabs
          defaultActiveKey="1"
          items={PlansTabsItems({
            plans,
            handlePlanChange,
            handleDurationChange,
            handleUpdateSubscription,
            setCustomDate,
            paymentMethod,
            setPaymentMethod,
            selectedDuration,
            expiaryDate,
            isCorporate,
            setNoLicenses,
            noLicenses,
            setBusinessName,
            selectedcustomerdata,
          })}
        />
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Button
            type="primary"
            htmlType="submit"
            className="up-modal-form-button"
          >
            Update Plan
          </Button>
        </div>
      </form>
      {customDate && (
        <ModalComponent
          title="Choose a Date"
          visible={customDate}
          onCancel={() => setCustomDate(false)}
          zIndex={1100}
          backdrop
          show={customDate}
          onHandleCancel={() => setCustomDate(false)}
          extraClassName="custom-date-modal"
          onOk={() => {
            setCustomDate(false);
          }}
        >
          <div className="custom-date-modal-choose-date">
            <span>
              Choose a Starting Date:&nbsp;
            </span>
            <DatePicker 
              onChange={onDateChange}
              getPopupContainer={trigger => trigger.parentNode}
              defaultValue={selectedDate}
            />
          </div>
          <div className="custom-date-modal-choose-date">
            <span>Choose a Plan Duration<span className="danger">*</span>:</span>
            <select
              id="duration"
              name="duration"
              onChange={handleCustomDateDurationChange}
              className="custom-date-modal-select"
            >
              <option value="">Select a plan</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="halfyearly">Half Yearly</option>
              <option value="annual">Annual</option>
            </select>
          </div>
          <Button
            type="primary"
            onClick={() => setCustomDate(false)}
            className="up-modal-form-button"
          >
            Done
          </Button>
        </ModalComponent>
      )}
    </div>
  );
}
