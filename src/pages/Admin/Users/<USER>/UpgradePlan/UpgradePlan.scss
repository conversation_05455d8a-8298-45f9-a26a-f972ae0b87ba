@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

$font: "Inter";

.up-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  &-choose-plan{
    margin-top: 1rem;
    margin-bottom: 0;
    color: #0A84FF;
  }
  &-details {
    display: flex;
    width: 100%;
    justify-content: space-between;
    gap: 2rem;
    &-table{
      padding: 1rem 0.5rem;
      background-color: #1890FF0F;
      border-radius: 10px;
      border: 1px solid #1890FF;
      margin: 1rem 0;
      table{
        tr{
          th{
            font-family: $font;
            font-size: 14px;
            font-weight: 300;
            color: #818088;
            padding: 0.5rem;
          }
          td{
            font-family: $font;
            font-size: 14px;
            font-weight: 600;
            color: #242424;
            padding: 0.5rem;
            .active{
              color: #008000;
              background-color: #00800035;
              padding: 0.2rem 0.5rem;
              border-radius: 6px;
              font-size: 12px;
            }
            .inactive{
              color: #c70a0a;
              background-color: #ff000020;
              padding: 0.2rem 0.5rem;
              border-radius: 6px;
              font-size: 12px;
            }
          }
        }
      }
    }
    &-outer {
      display: flex;
      flex-direction: column;
    }
    p {
      display: flex;
      font-size: 14px;
      font-family: $font;
      color: #242424;
      font-weight: 600;
    }
    &:nth-child(1) {
      p {
        strong {
          display: flex;
          width: 3.2rem;
          font-size: 14px;
          color: #818088;
          font-weight: 300;
          width: auto;
          margin-right: 0.5rem;
        }
      }
    }
    &:nth-child(2) {
      p {
        strong {
          font-size: 14px;
          display: flex;
          width: 5.5rem;
          color: #818088;
          font-weight: 300;
        }
      }
    }
  }
  &-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
    .ant-tabs-tab {
      font-family: $font;
      font-size: 20px;
      color: #818088;
      margin: 0;
      padding: 0 1rem;
      font-weight: 300;
      margin: 0 0.5rem;
    }
    .choose-subscription {
      label{
        font-family: $font;
        font-size: 14px;
      }
    }
    #subscription{
      font-family: $font;
      font-size: 14px;
    }
    &-table {
      display: flex;
      flex-direction: column;
      width: 90%;
      table-layout: fixed;
      margin: 1rem 0;
      #subscription{
        font-family: $font;
        padding: 0.2rem;
        border-radius: 4px;
        border: 1px solid #D9D9D9;
        width: 90%;
      }
      #duration{
        font-family: $font;
        padding: 0.2rem;
        border-radius: 4px;
        border: 1px solid #D9D9D9;
        width: 90%;
      }
      thead {
        width: 100%;
        position: relative;
        tr {
          width: 100%;
          display: flex;
          justify-content: space-between;
          position: relative;
          th {
            width: 100%;
            text-align: center;
            font-size: 15px;
            font-family: $font;
            display: flex;
            align-items: center;
            font-weight: 500;
            color: #878787;
            padding: 0.5rem;
          }
        }
      }
      tbody {
        width: 100%;
        position: relative;
        tr {
          width: 100%;
          display: flex;
          justify-content: space-between;
          position: relative;
          td {
            width: 100%;
            font-size: 14px;
            font-family: $font;
            padding: 0.5rem;
            display: flex;
            align-items: center;
          }
          .subscription-type{
            color: #242424;
            font-weight: 600;
            font-size: 18px;
          }
        }
      }
    }
    &-button{
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 0.4rem;
      span{
        font-family: $font;
        font-size: 14px;
      }
    }
  }
  .toast-message {
    padding: 1rem 2rem !important;
  }
  .plan-update-submit {
    display: flex;
    justify-content: center;
    &-button {
      border: none;
      background-color: #007bff;
      color: white;
      border-radius: 6px;
      width: max-content;
      width: 10rem;
      padding: 0.3rem 1rem;
    }
  }
}
.upgrade-plan-modal {
  display: flex !important;
  justify-content: center;
  // align-items: center;
  // height: 80vh !important;
  // overflow-y: auto;
  transform: translateY(4%);
  .modal-title{
    font-family: $font;
    font-size: 22px;
  }
  .modal-sub-title{
    font-family: $font;
    font-size: 14px;
    color: #277BF7;
    font-weight: 300;
  }
  .modal-dialog {
    margin: 0;
    max-width: 50rem;
    border-radius: 15px;
    height: 80%;
    // overflow-y: auto;
    background-color: #fff;
  }
  .modal-content {
    border-radius: 15px;
    height: 100%;
    width: 50rem;
    padding: 1rem;
    overflow-y: auto;
    @media screen and (max-width: 870px) {
      width: 40rem;
    }
    @media screen and (max-width: 670px) {
      width: 30rem;
    }
    @media screen and (max-width: 490px) {
      width: 20rem;
    }
  }
}
.choose-subscription{
  display: flex;
  justify-content: center;
  flex-direction: column;
  &-below{
    display: flex;
    gap: 3rem;
    flex-wrap: wrap;
    div{
      display: flex;
      flex-direction: column;
      align-items: center;
      .payment-method{
        flex-direction: row;
        label{
          display: flex;
          align-items: flex-start;
        }
      }
      label{
        text-align: center;
        font-size: 15px !important;
        font-family: "Inter";
        display: flex;
        align-items: center;
        font-weight: 500;
        color: #878787;
        padding: 0.5rem;
      }
      select{
        font-family: "Inter";
        padding: 0.2rem;
        border-radius: 4px;
        border: 1px solid #D9D9D9;
        width: 90%;
      }
    }
    .pay-method{
      align-items: flex-start;
    }
  }
}
.custom-date-modal{
  &-select{
    width: 60%;
    height: 2rem;
    border-radius: 6px;
    border: 1px solid #D9D9D9;
  }
  .modal-body{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  &-choose-date{
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    .danger{
      color: #FF4D4F;
    }
    .ant-picker{
      width: 60%;
      height: 2rem;
      border-radius: 6px;
    }
  }
}